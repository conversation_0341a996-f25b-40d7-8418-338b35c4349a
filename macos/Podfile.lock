PODS:
  - connectivity_plus (0.0.1):
    - Flutter
    - FlutterMacOS
  - device_info_plus (0.0.1):
    - FlutterMacOS
  - flutter_secure_storage_macos (6.1.3):
    - FlutterMacOS
  - FlutterMacOS (1.0.0)
  - local_auth_darwin (0.0.1):
    - Flutter
    - FlutterMacOS
  - open_file_mac (0.0.1):
    - FlutterMacOS
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - url_launcher_macos (0.0.1):
    - FlutterMacOS

DEPENDENCIES:
  - connectivity_plus (from `Flutter/ephemeral/.symlinks/plugins/connectivity_plus/darwin`)
  - device_info_plus (from `Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos`)
  - flutter_secure_storage_macos (from `Flutter/ephemeral/.symlinks/plugins/flutter_secure_storage_macos/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - local_auth_darwin (from `Flutter/ephemeral/.symlinks/plugins/local_auth_darwin/darwin`)
  - open_file_mac (from `Flutter/ephemeral/.symlinks/plugins/open_file_mac/macos`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin`)
  - shared_preferences_foundation (from `Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin`)
  - url_launcher_macos (from `Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos`)

EXTERNAL SOURCES:
  connectivity_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/connectivity_plus/darwin
  device_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos
  flutter_secure_storage_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_secure_storage_macos/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  local_auth_darwin:
    :path: Flutter/ephemeral/.symlinks/plugins/local_auth_darwin/darwin
  open_file_mac:
    :path: Flutter/ephemeral/.symlinks/plugins/open_file_mac/macos
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin
  shared_preferences_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin
  url_launcher_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos

SPEC CHECKSUMS:
  connectivity_plus: 18382e7311ba19efcaee94442b23b32507b20695
  device_info_plus: ce1b7762849d3ec103d0e0517299f2db7ad60720
  flutter_secure_storage_macos: c2754d3483d20bb207bb9e5a14f1b8e771abcdb9
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  local_auth_darwin: 66e40372f1c29f383a314c738c7446e2f7fdadc3
  open_file_mac: 0e554648e2a87ce59e9438e3e5ca3e552e90d89a
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  url_launcher_macos: c82c93949963e55b228a30115bd219499a6fe404

PODFILE CHECKSUM: 7eb978b976557c8c1cd717d8185ec483fd090a82

COCOAPODS: 1.16.2
