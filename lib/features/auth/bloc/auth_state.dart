import 'package:equatable/equatable.dart';

/// Authentication States
/// Defines all possible authentication states
abstract class AuthState extends Equatable {
  const AuthState();

  @override
  List<Object?> get props => [];
}

/// Initial state when the app starts
class AuthInitial extends AuthState {
  const AuthInitial();
}

/// State when checking authentication status
class AuthLoading extends AuthState {
  const AuthLoading();
}

/// State when user is authenticated
class AuthAuthenticated extends AuthState {
  final String accessToken;
  final String? userEmail;
  final Map<String, dynamic>? userProfile;

  const AuthAuthenticated({
    required this.accessToken,
    this.userEmail,
    this.userProfile,
  });

  @override
  List<Object?> get props => [accessToken, userEmail, userProfile];
}

/// State when user is not authenticated
class AuthUnauthenticated extends AuthState {
  const AuthUnauthenticated();
}

/// State when two-factor authentication is required
class AuthTwoFactorRequired extends AuthState {
  final String referenceCode;

  const AuthTwoFactorRequired({
    required this.referenceCode,
  });

  @override
  List<Object?> get props => [referenceCode];
}

/// State when OTP has been resent successfully
class AuthOtpResent extends AuthState {
  const AuthOtpResent();

  @override
  List<Object?> get props => [];
}

/// State when authentication fails
class AuthError extends AuthState {
  final String message;
  final String? errorCode;

  const AuthError({
    required this.message,
    this.errorCode,
  });

  @override
  List<Object?> get props => [message, errorCode];
}

/// State when signup is successful
class AuthSignupSuccess extends AuthState {
  final int timestamp;

  const AuthSignupSuccess({
    required this.timestamp,
  });

  @override
  List<Object?> get props => [timestamp];
}

/// State when contact verification is successful
class AuthContactVerificationSuccess extends AuthState {
  final String response;

  const AuthContactVerificationSuccess({
    required this.response,
  });

  @override
  List<Object?> get props => [response];
}

/// State when forgot password request is successful
class AuthForgotPasswordSuccess extends AuthState {
  final int timestamp;

  const AuthForgotPasswordSuccess({
    required this.timestamp,
  });

  @override
  List<Object?> get props => [timestamp];
}

/// State when password reset is successful
class AuthPasswordResetSuccess extends AuthState {
  const AuthPasswordResetSuccess();
}

/// State when two-factor authentication is enabled
class AuthTwoFactorEnabled extends AuthState {
  final List<String?>
      qrData; // [base64Image, clickableUrl] for app mode, [null, null] for SMS

  const AuthTwoFactorEnabled({
    required this.qrData,
  });

  @override
  List<Object?> get props => [qrData];
}

/// State when two-factor authentication is disabled
class AuthTwoFactorDisabled extends AuthState {
  const AuthTwoFactorDisabled();
}

/// State when token refresh is in progress
class AuthTokenRefreshing extends AuthState {
  const AuthTokenRefreshing();
}

/// State when logout is in progress
class AuthLoggingOut extends AuthState {
  const AuthLoggingOut();
}

/// State when logout is successful
class AuthLogoutSuccess extends AuthState {
  const AuthLogoutSuccess();
}
