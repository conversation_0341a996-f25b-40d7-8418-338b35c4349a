import 'package:equatable/equatable.dart';

/// Authentication Events
/// Defines all possible authentication events that can be triggered
abstract class AuthEvent extends Equatable {
  const AuthEvent();

  @override
  List<Object?> get props => [];
}

/// Event to check if user is already logged in
class AuthCheckRequested extends AuthEvent {
  const AuthCheckRequested();
}

/// Event to initiate login process
class AuthLoginRequested extends AuthEvent {
  final String email;
  final String password;

  const AuthLoginRequested({
    required this.email,
    required this.password,
  });

  @override
  List<Object?> get props => [email, password];
}

/// Event to handle biometric login
class AuthBiometricLoginRequested extends AuthEvent {
  final String email;

  const AuthBiometricLoginRequested({
    required this.email,
  });

  @override
  List<Object?> get props => [email];
}

/// Event to handle two-factor authentication
class AuthTwoFactorRequested extends AuthEvent {
  final String referenceCode;
  final String twoFactorCode;

  const AuthTwoFactorRequested({
    required this.referenceCode,
    required this.twoFactorCode,
  });

  @override
  List<Object?> get props => [referenceCode, twoFactorCode];
}

/// Event to resend OTP for two-factor authentication
class AuthResendOtpRequested extends AuthEvent {
  final String referenceCode;

  const AuthResendOtpRequested({
    required this.referenceCode,
  });

  @override
  List<Object?> get props => [referenceCode];
}

/// Event to refresh access token
class AuthTokenRefreshRequested extends AuthEvent {
  const AuthTokenRefreshRequested();
}

/// Event to logout user
class AuthLogoutRequested extends AuthEvent {
  const AuthLogoutRequested();
}

/// Event to perform global logout
class AuthGlobalLogoutRequested extends AuthEvent {
  const AuthGlobalLogoutRequested();
}

/// Event to handle signup
class AuthSignupRequested extends AuthEvent {
  final String activationCode;
  final String fullName;
  final String email;
  final String phone;

  const AuthSignupRequested({
    required this.activationCode,
    required this.fullName,
    required this.email,
    required this.phone,
  });

  @override
  List<Object?> get props => [activationCode, fullName, email, phone];
}

/// Event to handle contact verification
class AuthContactVerificationRequested extends AuthEvent {
  final String activationCode;
  final String password;
  final String emailCode;
  final String phoneCode;

  const AuthContactVerificationRequested({
    required this.activationCode,
    required this.password,
    required this.emailCode,
    required this.phoneCode,
  });

  @override
  List<Object?> get props => [activationCode, password, emailCode, phoneCode];
}

/// Event to handle forgot password
class AuthForgotPasswordRequested extends AuthEvent {
  final String email;

  const AuthForgotPasswordRequested({
    required this.email,
  });

  @override
  List<Object?> get props => [email];
}

/// Event to handle password reset
class AuthPasswordResetRequested extends AuthEvent {
  final String email;
  final String newPassword;
  final String resetCode;

  const AuthPasswordResetRequested({
    required this.email,
    required this.newPassword,
    required this.resetCode,
  });

  @override
  List<Object?> get props => [email, newPassword, resetCode];
}

/// Event to enable two-factor authentication
class AuthEnableTwoFactorRequested extends AuthEvent {
  final int mode; // 2 for SMS, 10/11 for app

  const AuthEnableTwoFactorRequested({
    required this.mode,
  });

  @override
  List<Object?> get props => [mode];
}

/// Event to disable two-factor authentication
class AuthDisableTwoFactorRequested extends AuthEvent {
  const AuthDisableTwoFactorRequested();
}
