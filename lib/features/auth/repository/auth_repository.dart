import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../models/auth_config.dart';

/// Authentication Repository
/// Handles data persistence and retrieval for authentication
class AuthRepository {
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage();

  // Token management
  static Future<void> saveTokens(String accessToken, String refreshToken) async {
    await _secureStorage.write(key: AuthConfig.accessTokenKey, value: accessToken);
    await _secureStorage.write(key: AuthConfig.refreshTokenKey, value: refreshToken);
  }

  static Future<String?> getAccessToken() async {
    return await _secureStorage.read(key: AuthConfig.accessTokenKey);
  }

  static Future<String?> getRefreshToken() async {
    return await _secureStorage.read(key: AuthConfig.refreshTokenKey);
  }

  static Future<void> clearTokens() async {
    await _secureStorage.delete(key: AuthConfig.accessTokenKey);
    await _secureStorage.delete(key: AuthConfig.refreshTokenKey);
  }

  // User credentials management
  static Future<void> saveUserCredentials(String email, String password) async {
    await _secureStorage.write(key: AuthConfig.emailKey, value: email);
    await _secureStorage.write(key: AuthConfig.passwordKey, value: password);
  }

  static Future<String?> getUserEmail() async {
    return await _secureStorage.read(key: AuthConfig.emailKey);
  }

  static Future<String?> getUserPassword() async {
    return await _secureStorage.read(key: AuthConfig.passwordKey);
  }

  static Future<void> clearUserCredentials() async {
    await _secureStorage.delete(key: AuthConfig.emailKey);
    await _secureStorage.delete(key: AuthConfig.passwordKey);
  }

  // Biometric settings
  static Future<void> setBiometricEnabled(bool enabled) async {
    await _secureStorage.write(key: AuthConfig.biometricKey, value: enabled.toString());
  }

  static Future<bool> isBiometricEnabled() async {
    String? biometric = await _secureStorage.read(key: AuthConfig.biometricKey);
    return biometric == 'true';
  }

  static Future<void> clearBiometricSettings() async {
    await _secureStorage.delete(key: AuthConfig.biometricKey);
  }

  // User profile data
  static Future<void> saveUserProfile(Map<String, dynamic> profile) async {
    await _secureStorage.write(key: AuthConfig.userProfileKey, value: profile.toString());
  }

  static Future<String?> getUserProfile() async {
    return await _secureStorage.read(key: AuthConfig.userProfileKey);
  }

  static Future<void> clearUserProfile() async {
    await _secureStorage.delete(key: AuthConfig.userProfileKey);
  }

  // Clear all authentication data
  static Future<void> clearAllAuthData() async {
    await clearTokens();
    await clearUserCredentials();
    await clearBiometricSettings();
    await clearUserProfile();
  }

  // Check if user has valid session
  static Future<bool> hasValidSession() async {
    String? accessToken = await getAccessToken();
    String? refreshToken = await getRefreshToken();
    return accessToken != null && refreshToken != null;
  }
}
