import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:http/http.dart' as http;
import 'package:in_app_update/in_app_update.dart';
import 'package:sms_autofill/sms_autofill.dart';

import '../../../changeNotifiers/NudronChangeNotifiers.dart';
import '../../../config.dart';
import '../../../main.dart';
import 'biometric_service.dart';
import '../../../utils/getDeviceID.dart';
import '../../../view_model/custom_exception.dart';
import '../models/auth_config.dart';

/// Authentication Service
/// Handles all authentication-related API calls and token management
class AuthService {
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage();
  static bool isLoggedIn = false;

  // Token Management
  static Future<String?> getAccessToken() async {
    if (kDebugMode) {
      print("Getting access token");
    }
    try {
      String? accessToken = await _secureStorage.read(key: 'access_token');
      if (accessToken == null || isTokenExpired(accessToken)) {
        if (kDebugMode) {
          print("access token expired. getting new");
        }
        return getNewAccessToken();
      } else if (isTokenExpiring(accessToken)) {
        //refresh in 10 seconds
        if (kDebugMode) {
          print("not yet expired but expiring. will get after 10s.");
        }
        print("Start");
        Future.delayed(const Duration(seconds: 10), () async {
          if (kDebugMode) {
            print("Getting access token after 10s.");
          }
          await getNewAccessToken();
        });
        if (kDebugMode) {
          print("returned old access token");
        }
        return accessToken;
      }
      if (kDebugMode) {
        print("access token fine. returning.");
      }
      return accessToken;
    } catch (e) {
      if (kDebugMode) print(e);
      await deleteDataAndLogout();
      throw CustomException('Please login again');
    }
  }

  /// Legacy compatibility method - delegates to getAccessToken()
  static Future<String?> getAccessToken2() async {
    return getAccessToken();
  }

  static Future<String?> getRefreshToken() async {
    String? refreshToken = await _secureStorage.read(key: 'refresh_token');
    return refreshToken;
  }

  static Future<String> getNewAccessToken() async {
    String? accessToken = await _secureStorage.read(key: 'access_token');
    if (accessToken != null && !isTokenExpiring(accessToken)) {
      return accessToken;
    }

    await updateApp();
    String? refToken = await getRefreshToken();
    if (refToken == null) {
      throw CustomException('Please login again');
    }

    final body = '04$refToken';
    final response = await _makeRequest(body);
    final splitResponse = response.split('|');
    if (response == '0') {
      throw CustomException('Redirecting to login page.. Please login again.');
    } else if (splitResponse.length == 2) {
      await _secureStorage.write(key: 'access_token', value: splitResponse[0]);
      await _secureStorage.write(key: 'refresh_token', value: splitResponse[1]);
      // Note: Profile update will be handled by AuthBloc when token refresh completes

      return splitResponse[0];
    } else {
      throw CustomException('Unexpected response');
    }
  }

  // Token Validation
  static DateTime getExpiryTime(String accessToken) {
    String payloadBase64 = accessToken.split('.')[1];
    String normalizedBase64 = base64.normalize(payloadBase64);

    String decodedToken = utf8.decode(base64.decode(normalizedBase64));
    Map<String, dynamic> tokenData = json.decode(decodedToken);
    int expiryTime = tokenData['exp'];

    // Return the expiry time as a DateTime object
    return DateTime.fromMillisecondsSinceEpoch(expiryTime * 1000);
  }

  static bool isTokenExpired(String accessToken) {
    DateTime expiryDateTime = getExpiryTime(accessToken);
    DateTime now = DateTime.now();
    return now.add(const Duration(seconds: 0)).isAfter(expiryDateTime);
  }

  static bool isTokenExpiring(String accessToken) {
    DateTime expiryDateTime = getExpiryTime(accessToken);
    DateTime now = DateTime.now();
    return now.add(const Duration(minutes: 30)).isAfter(expiryDateTime);
  }

  // Authentication Methods
  static Future<String?> login(String email, String password) async {
    if (ConfigurationCustom.skipAnyAuths) {
      return null;
    }
    //convert password to base64
    final passwordBase64 = base64.encode(utf8.encode(password));

    final body = '02$email|$passwordBase64';

    final response = await _makeRequest(body);
    final splitResponse = response.split('|');
    if (response == '0') {
      throw CustomException('Incorrect email or password');
    } else if (response == '10' || response == '01' || response == '00') {
      throw CustomException('Email or phone unverified');
    } else if (splitResponse.length == 2) {
      await BiometricService.checkAndStoreBiometric(email, password);
      await _secureStorage.write(key: 'access_token', value: splitResponse[0]);
      await _secureStorage.write(key: 'refresh_token', value: splitResponse[1]);
      await _secureStorage.write(key: 'email', value: email);
      await _secureStorage.write(key: 'password', value: password);
      await twoFAToggleVal(false);
    } else if (splitResponse.length == 1) {
      try {
        print("Init autofill");
        await SmsAutoFill().listenForCode();
        print("Init autofill done");
      } catch (e) {
        print("Error in parsing the otp.. $e");
      }
      await BiometricService.checkAndStoreBiometric(email, password);
      return response;
    } else {
      if (kDebugMode) {
        print(response);
      }
      throw CustomException('Unexpected response');
    }
    return null;
  }

  static Future<void> sendTwoFactorCode(
      String refCode, String twoFactorCode) async {
    final body = '03$refCode|$twoFactorCode';
    if (kDebugMode) {
      print(body);
    }
    final response = await _makeRequest(body);
    final splitResponse = response.split('|');
    if (response == '0') {
      throw CustomException('Incorrect code');
    } else if (response == '1') {
      throw CustomException('Code expired');
    } else if (splitResponse.length == 2) {
      await _secureStorage.write(key: 'access_token', value: splitResponse[0]);
      await _secureStorage.write(key: 'refresh_token', value: splitResponse[1]);
      await twoFAToggleVal(true);
    } else {
      throw CustomException('Unexpected response');
    }
  }

  /// Resend OTP for two-factor authentication
  /// Note: This is a placeholder - the actual implementation may need to be
  /// coordinated with the backend team to determine the correct API endpoint
  static Future<void> resendOtp(String referenceCode) async {
    // For now, this is a placeholder that simulates a successful resend
    // In a real implementation, this would call a specific resend endpoint
    if (kDebugMode) {
      print("Resending OTP for reference code: $referenceCode");
    }

    // Simulate network delay
    await Future.delayed(const Duration(seconds: 1));

    // In the future, this should make an actual API call like:
    // final body = '04$referenceCode'; // or whatever the correct format is
    // final response = await _makeRequest(body);
    // if (response == '0') {
    //   throw CustomException('Error resending OTP');
    // }
  }

  static Future<int> logout() async {
    const body = '08';
    if (kDebugMode) {
      print("logout");
    }

    final response = await _makeRequest(body, url: AuthConfig.au3Url);
    if (response == '0') {
      throw CustomException('Error processing request');
    }
    await deleteDataAndLogout();
    return int.parse(response);
  }

  static Future<int> globalLogout() async {
    const body = '09';
    final response = await _makeRequest(body, url: AuthConfig.au3Url);
    if (response == '0') {
      throw CustomException('Error processing request');
    }
    await deleteDataAndLogout();
    return int.parse(response);
  }

  // Registration Methods
  static Future<int> signUp(
      String actCode, String fullName, String email, String phone) async {
    final body = '00$actCode|$fullName|$email|$phone';
    final response = await _makeRequest(body);
    try {
      await SmsAutoFill().listenForCode();
    } catch (e) {
      print("Error in parsing the otp.. $e");
    }

    if (response == '0') {
      throw CustomException('Email already in use');
    }
    return int.parse(response);
  }

  static Future<String> contactVerification(
      String actCode, String pass, String emCode, String phCode) async {
    if (pass.length < 8) {
      throw CustomException('Password must be at least 8 characters');
    }
    final passwordBase64 = base64.encode(utf8.encode(pass));

    final body = '01$actCode|$passwordBase64|$emCode|$phCode';
    final response = await _makeRequest(body);
    if (response == '0') {
      throw CustomException('Incorrect email or phone code');
    }
    return response;
  }

  // Two-Factor Authentication
  static Future<void> twoFAToggleVal(bool twoFAEnabled) async {
    await _secureStorage.write(
        key: 'two_factor', value: twoFAEnabled.toString());
    NudronRandomStuff.isAuthEnabled.value = twoFAEnabled;
  }

  static Future<List<String?>> enableTwoFactorAuth(int mode) async {
    final body = '02$mode';
    final response = await _makeRequest(body, url: AuthConfig.au3Url);

    if (response == '0') {
      throw CustomException('Error processing request');
    }
    twoFAToggleVal(true);

    if (mode == 2) {
      return [null, null]; // Return null values if mode is 0
    }

    // Split the response by '|' and return the resulting list. first is the base64 image, second is the clickable url
    List<String?> responseValues = response.split('|');
    if (kDebugMode) {
      print("TWO FA RESPONSE");
      print(response);
      print(responseValues[1]);
    }

    return responseValues;
  }

  static Future<void> disableTwoFactorAuth() async {
    const body = '03';
    await _makeRequest(body, url: AuthConfig.au3Url);
    twoFAToggleVal(false);
  }

  // Utility Methods
  static Future<void> checkLogin() async {
    try {
      await getAccessToken();
      await refreshListeners();
      isLoggedIn = true;
    } catch (e) {
      isLoggedIn = false;
    }
  }

  static Future<void> refreshListeners() async {
    try {
      String? twoFactorEnabled = await _secureStorage.read(key: 'two_factor');
      NudronRandomStuff.isAuthEnabled.value = twoFactorEnabled == 'true';

      String? biometricEnabled = await _secureStorage.read(key: 'biometric');
      NudronRandomStuff.isBiometricEnabled.value = biometricEnabled == 'true';
    } catch (e) {
      print(e);
    }
  }

  static Future<Map<dynamic, dynamic>> tokenCheck() async {
    if (ConfigurationCustom.skipAnyAuths) {
      return {
        "email": "<EMAIL>",
        "emailVerified": true,
        "lastPassChange": "1720594664724",
        "lastUpdate": 1720967774113,
        "multiFactor": "0",
        "name": "John Doe",
        "phone": "+919845888888",
        "phoneVerified": true,
        "userID": "16170205-548a-487b-b9ee-4abdb624c550"
      };
    }
    const body = '07';

    final response = await _makeRequest(body, url: AuthConfig.au3Url);
    return jsonDecode(response);
  }

  // Password and Profile Management
  static Future<int> forgotPassword(String email) async {
    final body = '05$email';
    final response = await _makeRequest(body);
    if (response == '0') {
      throw CustomException('Error processing request');
    }
    return int.parse(response);
  }

  static Future<String> updateInfo(String oldPass, String fullName,
      String email, String phone, String newPass) async {
    final oldPassB64 = base64.encode(utf8.encode(oldPass));
    final newPassB64 = base64.encode(utf8.encode(newPass));

    final body = '00$oldPassB64|$fullName|$email|$phone|$newPassB64';
    final response = await _makeRequest(body, url: AuthConfig.au3Url);

    if (kDebugMode) {
      print(response);
    }
    if (response == '0') {
      throw CustomException('Incorrect old password');
    } else if (response == '1') {
      throw CustomException('Email already in use');
    } else if (response == '2') {
      throw CustomException('Number already in use');
    } else {
      return "Success";
    }
  }

  static Future<String> addProject(String activationCode) async {
    final body = '04$activationCode';
    final response = await _makeRequest(body, url: AuthConfig.au3Url);

    if (kDebugMode) {
      print(response);
    }
    if (response == '0') {
      throw CustomException('Incorrect activation code. Please check again');
    } else {
      return response;
    }
  }

  // Verification Methods
  static Future<String> verifyEmailPhone(String emCode, String phCode) async {
    final body = '10$emCode|$phCode';
    final response = await _makeRequest(body, url: AuthConfig.au3Url);
    if (response == '00' ||
        response == '02' ||
        response == '10' ||
        response == '01') {
      if (emCode.isNotEmpty) responseHandler(response[0], "Email");
      if (phCode.isNotEmpty) responseHandler(response[1], "Phone");
      return "Email and Phone number verified!";
    } else {
      throw CustomException('Unexpected response');
    }
  }

  static String responseHandler(String response, String emailOrPhone) {
    if (response == '0') {
      throw CustomException('Incorrect code for $emailOrPhone');
    } else if (response == '2') {
      throw CustomException('The code is expired for $emailOrPhone');
    }
    return response;
  }

  // Data Management
  static deleteStoredData() async {
    isLoggedIn = false;
    //check if biometric is enabled
    String? biometricEnabled = await _secureStorage.read(key: 'biometric');
    String? themeMode = await _secureStorage.read(key: 'themeMode');
    if (biometricEnabled == 'true') {
      //read key user and pwd
      String? email = await _secureStorage.read(key: 'email');
      String? password = await _secureStorage.read(key: 'password');
      //delete all except these 3 keys
      await _secureStorage.deleteAll();
      //write back email and password
      await _secureStorage.write(key: 'email', value: email);
      await _secureStorage.write(key: 'password', value: password);
      await _secureStorage.write(key: 'biometric', value: 'true');
    } else {
      await _secureStorage.deleteAll();
    }
    if (themeMode != null) {
      await _secureStorage.write(key: 'themeMode', value: themeMode);
    }
  }

  static Future<void> deleteDataAndLogout() async {
    await deleteStoredData();
    mainNavigatorKey.currentState
        ?.pushNamedAndRemoveUntil('/login', (route) => false);
  }

  // App Update
  static Future<void> updateApp() async {
    if (Platform.isAndroid && kReleaseMode) {
      try {
        AppUpdateInfo updateInfo = await InAppUpdate.checkForUpdate();

        if (updateInfo.updateAvailability ==
            UpdateAvailability.updateAvailable) {
          try {
            AppUpdateResult result = await InAppUpdate.performImmediateUpdate();
            if (result == AppUpdateResult.inAppUpdateFailed) {
              await InAppUpdate.startFlexibleUpdate();
            }
          } catch (e) {
            print('Failed to update the app: $e');
            rethrow;
          }
        }
      } catch (e) {
        await deleteDataAndLogout();
        print("Error: $e");
        throw CustomException(
            "Redirecting to login page.. Please update app and login again");
      }
    }
  }

  // HTTP Request Handler
  static Future<String> _makeRequest(String body,
      {String url = AuthConfig.au1Url, Duration? timeout}) async {
    DateTime now = DateTime.now();
    final connectivityResult = await (Connectivity().checkConnectivity());
    if (connectivityResult == ConnectivityResult.none) {
      throw CustomException('No internet connection');
    }
    final jwt = (url == AuthConfig.au3Url) ? await getAccessToken() : null;
    String userAgent = await DeviceInfoUtil.getUserAgent();

    final headers = {
      'User-Agent': userAgent,
      'medium': 'phone',
      'Content-Type': 'text/plain',
      if (jwt != null) 'Authorization': 'Bearer $jwt',
      if (url == AuthConfig.au1Url) 'tenantID': AuthConfig.tenantId,
      if (url == AuthConfig.au1Url) 'clientID': AuthConfig.clientId,
    };
    var request = http.Request('POST', Uri.parse(url));
    request.body = body;
    request.headers.addAll(headers);
    if (kDebugMode) {
      print("url ${request.url}");
      print("body ${request.body}");
      print("header ${request.headers}");
    }
    try {
      http.StreamedResponse response =
          await request.send().timeout(timeout ?? const Duration(seconds: 5));

      DateTime later = DateTime.now();

      if (kDebugMode) {
        print("Time taken: ");
        print(later.difference(now).inMilliseconds);
      }
      if (kDebugMode) {
        print(response.statusCode);
      }
      if (response.statusCode == 200) {
        var resp = await response.stream.bytesToString();
        if (kDebugMode) {
          print(resp);
        }
        return resp;
      } else if (response.statusCode == 401 || response.statusCode == 403) {
        await deleteDataAndLogout();
        throw CustomException('Redirecting to login page.. Please login again');
      } else {
        String responseBody = await response.stream.bytesToString();
        print(responseBody);
        throw CustomException(responseBody);
      }
    } on TimeoutException {
      throw CustomException('Request timed out');
    }
  }

  /// Reset password using email, new password, and reset code
  static Future<void> resetPassword(
      String email, String newPassword, String resetCode) async {
    try {
      final Map<String, dynamic> requestBody = {
        'email': email,
        'newPassword': newPassword,
        'resetCode': resetCode,
        'tenantId': AuthConfig.tenantId,
        'clientId': AuthConfig.clientId,
      };

      final response = await _makeRequest(
        jsonEncode(requestBody),
        url: '${AuthConfig.au1Url}/resetPassword',
      );

      if (kDebugMode) {
        print('Password reset successful: $response');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Password reset failed: $e');
      }
      throw CustomException('Failed to reset password: ${e.toString()}');
    }
  }
}
