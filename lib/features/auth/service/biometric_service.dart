import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:local_auth/local_auth.dart';

import '../../../changeNotifiers/NudronChangeNotifiers.dart';
import '../models/auth_config.dart';

/// Biometric Authentication Service
/// Handles all biometric authentication functionality
class BiometricService {
  final LocalAuthentication _localAuth = LocalAuthentication();
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage();

  // Function to check if the device supports biometric authentication
  Future<bool> isBiometricSupported() async {
    try {
      return await _localAuth.canCheckBiometrics;
    } catch (e) {
      return false;
    }
  }

  // Function to check if biometric authentication is set up on the device
  Future<bool> isBiometricSetup() async {
    try {
      bool canCheckBiometrics = await isBiometricSupported();
      if (canCheckBiometrics) {
        List<BiometricType> availableBiometrics =
            await _localAuth.getAvailableBiometrics();
        return availableBiometrics.isNotEmpty;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  Future<String> getPassword() async {
    return await _secureStorage.read(key: AuthConfig.passwordKey) ?? '';
  }

  // Function to authenticate the user using biometrics
  Future<bool> authenticateWithBiometrics(
      {String reason = 'Authenticate to proceed'}) async {
    try {
      bool isSetup = await isBiometricSetup();
      if (!isSetup) return false;

      bool authenticated = await _localAuth.authenticate(
        localizedReason: reason,
        options: const AuthenticationOptions(
          useErrorDialogs: true,
          stickyAuth: true,
        ),
      );
      return authenticated;
    } catch (e) {
      print(e);
      return false;
    }
  }

  // Function to check if biometric authentication was successful
  Future<bool> isCorrectBiometric() async {
    try {
      return await authenticateWithBiometrics(
          reason: 'Authenticate to confirm your identity');
    } catch (e) {
      return false;
    }
  }

  Future<void> toggleBiometric(bool value) async {
    NudronRandomStuff.isBiometricEnabled.value = value;
    await _secureStorage
        .write(key: AuthConfig.biometricKey, value: value.toString());
  }

  static Future<String?> isBiometricEnabled() async {
    String? biometric = await _secureStorage.read(key: AuthConfig.biometricKey);
    String? email = await _secureStorage.read(key: AuthConfig.emailKey);
    if(kDebugMode)
    print('biometric: $biometric, email: $email');
    // Note: We'll need to import AuthService to check isLoggedIn status
    // For now, we'll use a placeholder that needs to be updated
    if (biometric != null && email != null && biometric == 'true') {
      return email;
    }
    return null;
  }

  static checkAndStoreBiometric(String email, String password) async {
    String? emailst = await _secureStorage.read(key: AuthConfig.emailKey);
    if ((emailst != null && emailst != email) || emailst == null) {
      await _secureStorage.delete(key: AuthConfig.biometricKey);
    }
    await _secureStorage.write(key: AuthConfig.emailKey, value: email);
    await _secureStorage.write(key: AuthConfig.passwordKey, value: password);
  }
}
