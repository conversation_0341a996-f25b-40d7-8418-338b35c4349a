import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import 'package:provider/provider.dart';
import 'package:sms_autofill/sms_autofill.dart';
import 'package:water_metering/utils/pok.dart';

import '../../../theme/theme2.dart';
import '../../../utils/alert_message.dart';
import '../../../views/widgets/containers/CustomAppBar.dart';
import '../../../views/widgets/containers/customButton.dart';
import '../bloc/auth_bloc_exports.dart';

class EnterTwoFacCode extends StatefulWidget {
  const EnterTwoFacCode({super.key, required this.referenceCode});

  final String referenceCode;

  @override
  State<EnterTwoFacCode> createState() => _EnterTwoFacCodeState();
}

class _EnterTwoFacCodeState extends State<EnterTwoFacCode> with CodeAutoFill {
  TextEditingController otpFieldController = TextEditingController();

  printdevicehash() async {
    String a = await SmsAutoFill().getAppSignature;
    print("Getting my device code: $a. ");
  }

  @override
  void initState() {
    printdevicehash();
    listenForCode();

    SystemChrome.setPreferredOrientations(
        [DeviceOrientation.portraitDown, DeviceOrientation.portraitUp]);
    super.initState();
  }

  @override
  void codeUpdated() {
    if (kDebugMode) {
      print("OTP is $code");
    }
    setState(() {
      otpFieldController.text = code!;
    });
  }

  @override
  void dispose() {
    SmsAutoFill().unregisterListener();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthBloc, AuthState>(
      listener: (context, state) {
        if (state is AuthAuthenticated) {
          CustomAlert.showCustomScaffoldMessenger(
            context,
            "Successfully logged in! Redirecting to home page...",
            AlertType.success,
          );
          Navigator.of(context).pushNamedAndRemoveUntil("/", (route) => false);
        } else if (state is AuthOtpResent) {
          CustomAlert.showCustomScaffoldMessenger(
            context,
            "OTP resent successfully!",
            AlertType.success,
          );
        } else if (state is AuthError) {
          CustomAlert.showCustomScaffoldMessenger(
            context,
            state.message,
            AlertType.error,
          );
        }
      },
      child: GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
        },
        child: SafeArea(
          child: Scaffold(
            appBar: CustomAppBar(
              choiceAction: null,
            ),
            backgroundColor:
                Provider.of<ThemeNotifier>(context).currentTheme.bgColor,
            resizeToAvoidBottomInset: false,
            body: SizedBox(
              height: 1.sh - 51.h,
              width: 1.sw,
              child: Stack(
                children: [
                  Align(
                    alignment: Alignment.bottomCenter,
                    child: Transform.rotate(
                      angle: 0,
                      child: SvgPicture.asset(
                        'assets/icons/nfcicon.svg',
                        fit: BoxFit.cover,
                        clipBehavior: Clip.hardEdge,
                        colorFilter: ColorFilter.mode(
                          CommonColors.blue.withValues(alpha: 0.25),
                          BlendMode.srcIn,
                        ),
                        width: 450.minSp,
                      ),
                    ),
                  ),
                  Scaffold(
                    backgroundColor: Colors.transparent,
                    resizeToAvoidBottomInset: true,
                    body: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            height: 3.h,
                            color: CommonColors.blue,
                          ),
                          SizedBox(height: 20.h),
                          Center(
                            child: Text(
                              'Nudron IoT Solutions',
                              style: GoogleFonts.roboto(
                                  fontSize: 37.minSp,
                                  fontWeight: FontWeight.bold,
                                  color: Provider.of<ThemeNotifier>(context)
                                      .currentTheme
                                      .loginTitleColor),
                            ),
                          ),
                          SizedBox(height: 10.h),
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 16.w),
                            child: Center(
                              child: Container(
                                child: Text(
                                    "Welcome to Nudron's Water Metering Dashboard",
                                    textAlign: TextAlign.center,
                                    style: GoogleFonts.roboto(
                                        fontSize: ThemeNotifier.medium.minSp,
                                        color:
                                            Provider.of<ThemeNotifier>(context)
                                                .currentTheme
                                                .basicAdvanceTextColor)),
                              ),
                            ),
                          ),
                          SizedBox(height: 20.h),
                          Center(
                            child: Text(
                              'ENTER 2FA CODE',
                              style: GoogleFonts.roboto(
                                  fontSize: 25.minSp,
                                  fontWeight: FontWeight.bold,
                                  color: Provider.of<ThemeNotifier>(context)
                                      .currentTheme
                                      .loginTitleColor),
                            ),
                          ),
                          SizedBox(height: 40.h),
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 35.w),
                            child: PinCodeTextField(
                              appContext: context,
                              pastedTextStyle: TextStyle(
                                color: Colors.green.shade600,
                                fontWeight: FontWeight.bold,
                              ),
                              length: 6,
                              obscureText: false,
                              obscuringCharacter: '*',
                              blinkWhenObscuring: true,
                              animationType: AnimationType.fade,
                              validator: (v) {
                                if (v!.length < 3) {
                                  return "I'm from validator";
                                } else {
                                  return null;
                                }
                              },
                              pinTheme: PinTheme(
                                shape: PinCodeFieldShape.box,
                                borderRadius: BorderRadius.circular(5),
                                fieldHeight: 50.h,
                                fieldWidth: 40.w,
                                activeFillColor: Colors.white,
                                inactiveFillColor: Colors.white,
                                selectedFillColor: Colors.white,
                                activeColor: CommonColors.blue,
                                inactiveColor: Colors.grey,
                                selectedColor: CommonColors.blue,
                              ),
                              cursorColor: Colors.black,
                              animationDuration:
                                  const Duration(milliseconds: 300),
                              enableActiveFill: true,
                              controller: otpFieldController,
                              keyboardType: TextInputType.number,
                              boxShadows: const [
                                BoxShadow(
                                  offset: Offset(0, 1),
                                  color: Colors.black12,
                                  blurRadius: 10,
                                )
                              ],
                              onCompleted: (v) {
                                if (kDebugMode) {
                                  print("Completed");
                                }
                              },
                              onChanged: (value) {
                                if (kDebugMode) {
                                  print(value);
                                }
                                setState(() {});
                              },
                              beforeTextPaste: (text) {
                                if (kDebugMode) {
                                  print("Allowing to paste $text");
                                }
                                return true;
                              },
                            ),
                          ),
                          SizedBox(height: 40.h),
                          Center(
                            child: CustomButton(
                              text: "VERIFY",
                              onPressed: () async {
                                FocusScope.of(context).unfocus();
                                if (otpFieldController.text.isEmpty ||
                                    otpFieldController.text.length < 6) {
                                  CustomAlert.showCustomScaffoldMessenger(
                                      context,
                                      "Please Enter 6 digit OTP",
                                      AlertType.warning);
                                  return;
                                }
                                // Use BLoC instead of direct service call
                                context.read<AuthBloc>().add(
                                      AuthTwoFactorRequested(
                                        referenceCode: widget.referenceCode,
                                        twoFactorCode: otpFieldController.text,
                                      ),
                                    );
                              },
                            ),
                          ),
                          SizedBox(height: 40.h),
                          Center(
                            child: CustomButton(
                              text: "RESEND CODE",
                              isRed: true,
                              onPressed: () async {
                                // Use BLoC for resend OTP
                                context.read<AuthBloc>().add(
                                      AuthResendOtpRequested(
                                        referenceCode: widget.referenceCode,
                                      ),
                                    );
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
