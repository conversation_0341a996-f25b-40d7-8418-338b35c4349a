import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:water_metering/bloc/dashboardBloc/dashboardBloc.dart';
import 'package:water_metering/bloc/dashboardBloc/dashboardState.dart';
import 'package:water_metering/theme/theme2.dart';
import 'package:water_metering/utils/pok.dart';

import '../table/DataGridWidget.dart';
import '../widgets/containers/BillingFormula.dart';
import '../widgets/containers/CustomMonthDropDown.dart';

class SummaryTable extends StatefulWidget {
  const SummaryTable({super.key});

  @override
  State<SummaryTable> createState() => _SummaryTableState();
}

class _SummaryTableState extends State<SummaryTable> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: Provider.of<ThemeNotifier>(context).currentTheme.bgColor,
      // body: LayoutBuilder(builder: (context, constraints) {
      //   print("SummaryTable: ${constraints.maxHeight}");
      //   return Column(
      //     children: [
      //       Container(
      //         height: 3.minSp,
      //         color: CommonColors.green,
      //       ),
      //       SizedBox(
      //         height: constraints.maxHeight - 3.minSp,
      //         child: Center(
      //             child: Text("COMING SOON",
      //                 style: GoogleFonts.roboto(
      //                     fontSize: 20.sp,
      //                     color: Provider.of<ThemeNotifier>(context)
      //                         .currentTheme
      //                         .noEntriesColor,
      //                     fontWeight: FontWeight.w500))),
      //       ),
      //     ],
      //   );
      // })
      body: Column(
        children: [
          Container(
            height: 3.minSp,
            color: CommonColors.green,
          ),
          // CustomMonthDropdown at the top
          const CustomMonthDropdown(),
          Container(
            height: 3.minSp,
            color: CommonColors.green,
          ),
          const BillingFormula(),
          Container(
            height: 3.minSp,
            color: CommonColors.green,
          ),
          // BlocBuilder takes the remaining space
          Expanded(
            child: BlocBuilder<DashboardBloc, DashboardState>(
              buildWhen: (previous, current) {
                if (current is RefreshSummaryPage ||
                    current is RefreshSummaryPage2) {
                  return true;
                }
                return false;
              },
              builder: (context, state) {
                final data =
                    BlocProvider.of<DashboardBloc>(context).summaryData;

                return DataGridWidget(
                  data: data,
                  key: UniqueKey(),
                  columnsToTakeHeaderWidthAndExtraPadding: {
                    0: 20.minSp.toInt(),
                    1: 0,
                  },
                  frozenColumns: 2,
                  location: 'billing',
                );
              },
            ),
          ),
          Container(
            height: 3.h,
            color: CommonColors.green,
          ),
        ],
      ),
    );
  }
}