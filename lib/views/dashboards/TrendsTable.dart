import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:water_metering/bloc/dashboardBloc/dashboardBloc.dart';
import 'package:water_metering/model/chartModels.dart';
import 'package:water_metering/utils/utils.dart';

import '../table/DataGridWidget.dart';

class TrendsTable extends StatefulWidget {
  const TrendsTable({super.key});

  @override
  State<TrendsTable> createState() => _TrendsTableState();
}

class _TrendsTableState extends State<TrendsTable> {
  @override
  Widget build(BuildContext context) {
    return Container(
      child: ValueListenableBuilder<int>(
          valueListenable: NudronChartMap.selectedMonth,
          builder: (context, value, child) {
            return DataGridWidget(
              data: Utils.sortTableDataByMonthDescending(
                      BlocProvider.of<DashboardBloc>(context)
                          .nudronChartData
                          ?.getCurrentTableData()) ??
                  [],
              exportToIncludeWholeData: true,
              columnsToTakeHeaderWidthAndExtraPadding: {
                0: 20,
              },
              key: UniqueKey(),
            );
          }),
    );
  }
}